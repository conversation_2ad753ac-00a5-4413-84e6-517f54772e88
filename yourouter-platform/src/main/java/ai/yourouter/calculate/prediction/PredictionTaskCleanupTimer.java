package ai.yourouter.calculate.prediction;

import ai.yourouter.common.utils.SystemClock;
import ai.yourouter.jpa.prediction.task.bean.PredictionTask;
import ai.yourouter.jpa.prediction.task.repository.PredictionTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 预测任务清理定时器
 * 负责清理超时任务和过期任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PredictionTaskCleanupTimer {

    private final PredictionTaskRepository predictionTaskRepository;

    // 任务超时时间：30分钟
    private static final long TASK_TIMEOUT_MS = 30 * 60 * 1000L;
    
    // 任务过期时间：7天
    private static final long TASK_EXPIRE_MS = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 每5分钟检查一次超时任务
     */
    @Scheduled(cron = "0 */5 * * * *")
    @Transactional(rollbackFor = Exception.class)
    public void handleTimeoutTasks() {
        long now = SystemClock.now();
        long timeoutThreshold = now - TASK_TIMEOUT_MS;
        
        try {
            List<PredictionTask> timeoutTasks = predictionTaskRepository.findTimeoutRunningTasks(timeoutThreshold);
            
            if (!timeoutTasks.isEmpty()) {
                log.info("发现 {} 个超时任务，开始处理", timeoutTasks.size());
                
                for (PredictionTask task : timeoutTasks) {
                    predictionTaskRepository.updateTaskError(
                            task.getTaskId(),
                            PredictionTask.TaskStatus.FAILED.getCode(),
                            "任务执行超时",
                            now
                    );
                    log.warn("任务超时被标记为失败 | taskId: {} | createTime: {}", 
                            task.getTaskId(), task.getCreateTime());
                }
                
                log.info("超时任务处理完成，共处理 {} 个任务", timeoutTasks.size());
            }
        } catch (Exception e) {
            log.error("处理超时任务失败", e);
        }
    }

    /**
     * 每天凌晨2点清理过期任务
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Transactional(rollbackFor = Exception.class)
    public void cleanupExpiredTasks() {
        long now = SystemClock.now();
        long expireThreshold = now - TASK_EXPIRE_MS;
        
        try {
            int deletedCount = predictionTaskRepository.deleteExpiredTasks(expireThreshold);
            
            if (deletedCount > 0) {
                log.info("清理过期任务完成，共删除 {} 个任务", deletedCount);
            } else {
                log.debug("没有发现过期任务需要清理");
            }
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
        }
    }

    /**
     * 每小时统计任务状态
     */
    @Scheduled(cron = "0 0 * * * *")
    public void logTaskStatistics() {
        try {
            List<PredictionTask> runningTasks = predictionTaskRepository.findByStatusOrderByCreateTimeDesc(
                    PredictionTask.TaskStatus.RUNNING.getCode());
            List<PredictionTask> completedTasks = predictionTaskRepository.findByStatusOrderByCreateTimeDesc(
                    PredictionTask.TaskStatus.COMPLETED.getCode());
            List<PredictionTask> failedTasks = predictionTaskRepository.findByStatusOrderByCreateTimeDesc(
                    PredictionTask.TaskStatus.FAILED.getCode());
            
            log.info("预测任务统计 | 运行中: {} | 已完成: {} | 失败: {}", 
                    runningTasks.size(), completedTasks.size(), failedTasks.size());
        } catch (Exception e) {
            log.error("统计任务状态失败", e);
        }
    }
}
