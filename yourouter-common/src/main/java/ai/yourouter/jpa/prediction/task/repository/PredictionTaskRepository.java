package ai.yourouter.jpa.prediction.task.repository;

import ai.yourouter.jpa.prediction.task.bean.PredictionTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 预测任务Repository
 */
@Repository
public interface PredictionTaskRepository extends JpaRepository<PredictionTask, Long> {

    /**
     * 根据任务ID查找任务
     */
    Optional<PredictionTask> findByTaskId(String taskId);

    /**
     * 根据组织ID查找任务列表
     */
    List<PredictionTask> findByOrganizationIdOrderByCreateTimeDesc(Long organizationId);

    /**
     * 根据状态查找任务列表
     */
    List<PredictionTask> findByStatusOrderByCreateTimeDesc(Integer status);

    /**
     * 根据组织ID和状态查找任务列表
     */
    List<PredictionTask> findByOrganizationIdAndStatusOrderByCreateTimeDesc(Long organizationId, Integer status);

    /**
     * 更新任务状态
     */
    @Modifying
    @Query("UPDATE PredictionTask p SET p.status = :status, p.completeTime = :completeTime WHERE p.taskId = :taskId")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") Integer status, @Param("completeTime") Long completeTime);

    /**
     * 更新任务结果
     */
    @Modifying
    @Query("UPDATE PredictionTask p SET p.status = :status, p.responseBody = :responseBody, p.completeTime = :completeTime, p.responseTime = :responseTime, p.durationMs = :durationMs WHERE p.taskId = :taskId")
    int updateTaskResult(@Param("taskId") String taskId, 
                        @Param("status") Integer status, 
                        @Param("responseBody") String responseBody, 
                        @Param("completeTime") Long completeTime,
                        @Param("responseTime") Long responseTime,
                        @Param("durationMs") Long durationMs);

    /**
     * 更新任务错误信息
     */
    @Modifying
    @Query("UPDATE PredictionTask p SET p.status = :status, p.errorMessage = :errorMessage, p.completeTime = :completeTime WHERE p.taskId = :taskId")
    int updateTaskError(@Param("taskId") String taskId, 
                       @Param("status") Integer status, 
                       @Param("errorMessage") String errorMessage, 
                       @Param("completeTime") Long completeTime);

    /**
     * 查找超时的运行中任务
     */
    @Query("SELECT p FROM PredictionTask p WHERE p.status = 0 AND p.createTime < :timeoutThreshold")
    List<PredictionTask> findTimeoutRunningTasks(@Param("timeoutThreshold") Long timeoutThreshold);

    /**
     * 删除过期的已完成任务
     */
    @Modifying
    @Query("DELETE FROM PredictionTask p WHERE p.status IN (1, 2) AND p.completeTime < :expireThreshold")
    int deleteExpiredTasks(@Param("expireThreshold") Long expireThreshold);
}
