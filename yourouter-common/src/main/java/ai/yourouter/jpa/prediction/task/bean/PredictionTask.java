package ai.yourouter.jpa.prediction.task.bean;

import jakarta.persistence.*;
import lombok.*;

/**
 * 预测任务实体
 * 用于存储长时间运行的预测任务信息
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_prediction_task")
public class PredictionTask {

    @Id
    private Long id;

    /**
     * 任务ID，用于外部查询
     */
    @Column(unique = true, nullable = false)
    private String taskId;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 请求内容
     */
    @Column(columnDefinition = "text")
    private String requestBody;

    /**
     * 响应内容
     */
    @Column(columnDefinition = "text")
    private String responseBody;

    /**
     * 任务状态
     * 0: 进行中
     * 1: 已完成
     * 2: 失败
     */
    private Integer status;

    /**
     * 错误信息
     */
    @Column(columnDefinition = "text")
    private String errorMessage;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 请求时间
     */
    private Long requestTime;

    /**
     * 响应时间
     */
    private Long responseTime;

    /**
     * 持续时间（毫秒）
     */
    private Long durationMs;

    /**
     * 厂商信息
     */
    private String vendor;

    /**
     * 密钥ID
     */
    private String keyId;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        RUNNING(0, "进行中"),
        COMPLETED(1, "已完成"),
        FAILED(2, "失败");

        private final int code;
        private final String description;

        TaskStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskStatus fromCode(int code) {
            for (TaskStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown task status code: " + code);
        }
    }
}
