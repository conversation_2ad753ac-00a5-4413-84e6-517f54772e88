package ai.yourouter.chat.controller;

import ai.yourouter.chat.service.impl.GeminiMessagesServiceImpl;
import ai.yourouter.chat.util.ResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.CorePublisher;

import java.util.HashMap;

/**
 * Gemini 原生 API 控制器
 * 处理 Gemini 原生格式的请求
 */
@Slf4j
@RestController
@RequestMapping("/v1/projects/cognition/locations/us/publishers/google/models")
@RequiredArgsConstructor
@SuppressWarnings("ReactiveStreamsUnusedPublisher")
public class GeminiMessagesController {

    private final GeminiMessagesServiceImpl geminiMessagesService;

    /**
     * Gemini generateContent API 端点 (非流式)
     * 支持原生 Gemini API 格式
     */
    @PostMapping("/{model}:generateContent")
    public ResponseEntity<? extends CorePublisher<?>> generateContent(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        return buildGeminiNonStreamResponse(req);
    }

    /**
     * Gemini streamGenerateContent API 端点 (流式)
     * 支持原生 Gemini API 格式
     */
    @PostMapping("/{model}:predictLongRunning")
    public ResponseEntity<? extends CorePublisher<?>> predictLongRunning(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
    }

    @PostMapping("/{model}:fetchPredictOperation")
    public ResponseEntity<? extends CorePublisher<?>> fetchPredictOperation(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
    }

    /**
     * 查询veo3模型数据
     * 提供veo3模型的基本信息和价格数据
     */
    @GetMapping("/veo3/info")
    public ResponseEntity<? extends CorePublisher<?>> getVeo3Info() {
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.getVeo3ModelInfo());
    }

    /**
     * 查询指定模型数据
     * 提供指定模型的基本信息和价格数据
     */
    @GetMapping("/model/{modelName}/info")
    public ResponseEntity<? extends CorePublisher<?>> getModelInfo(@PathVariable String modelName) {
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.getModelInfo(modelName));
    }




    /**
     * 构建 Gemini 流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiStreamResponse(HashMap<String, Object> req) {

        return ResponseUtils.createStreamResponse(geminiMessagesService.streamCompletion(req));
    }

    /**
     * 构建 Gemini 非流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiNonStreamResponse(HashMap<String, Object> req) {
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.completion(req));
    }

}
