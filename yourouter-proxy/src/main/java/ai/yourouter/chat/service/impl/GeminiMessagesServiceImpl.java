package ai.yourouter.chat.service.impl;

import ai.yourouter.chat.channel.GeminiRemoteService;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.service.AbstractRemoteServiceTemplate;
import ai.yourouter.common.service.AsyncUsageStatisticsService;
import ai.yourouter.calculate.llm.OrganizationLLMCalculatePrice;
import ai.yourouter.common.utils.CharacterLLMStatisticsConverter;
import ai.yourouter.jpa.system.model.info.bean.SystemModelInfo;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import ai.yourouter.jpa.system.model.price.bean.SystemLLMPrice;
import ai.yourouter.jpa.system.model.price.repository.SystemLLMPriceRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Gemini 消息服务实现
 * 处理 Gemini 原生 API 的业务逻辑
 */
@Slf4j
@Service
public class GeminiMessagesServiceImpl extends AbstractRemoteServiceTemplate {

    private final GeminiRemoteService geminiRemoteService;
    private final OrganizationLLMCalculatePrice organizationLLMCalculatePrice;
    private final AsyncUsageStatisticsService asyncUsageStatisticsService;
    private final SystemModelInfoRepository systemModelInfoRepository;
    private final SystemLLMPriceRepository systemLLMPriceRepository;

    public GeminiMessagesServiceImpl(RetryConfig retryConfig,
                                   KmgRemoteService kmgRemoteService,
                                   GeminiRemoteService geminiRemoteService,
                                   OrganizationLLMCalculatePrice organizationLLMCalculatePrice,
                                   AsyncUsageStatisticsService asyncUsageStatisticsService,
                                   SystemModelInfoRepository systemModelInfoRepository,
                                   SystemLLMPriceRepository systemLLMPriceRepository) {
        super(retryConfig, kmgRemoteService);
        this.geminiRemoteService = geminiRemoteService;
        this.organizationLLMCalculatePrice = organizationLLMCalculatePrice;
        this.asyncUsageStatisticsService = asyncUsageStatisticsService;
        this.systemModelInfoRepository = systemModelInfoRepository;
        this.systemLLMPriceRepository = systemLLMPriceRepository;
    }

    /**
     * Gemini 非流式消息完成
     */
    @SneakyThrows
    public Mono<Object> completion(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);

            Mono<Object> operation = getAvailableKey(chatContext.apiModelName(), chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return geminiRemoteService.nonStreamGeminiCompletion(req, chatContext);
                    });

            return executeWithRetry(operation, "Gemini 非流式请求", chatContext);
        });
    }

    /**
     * Gemini 流式消息完成
     */
    @SneakyThrows
    public Flux<ServerSentEvent<String>> streamCompletion(HashMap<String, Object> req) {
        return Flux.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);

            Flux<ServerSentEvent<String>> streamOperation = getAvailableKeyForStream(chatContext.apiModelName(), chatContext)
                    .flatMapMany(key -> {
                        chatContext.setKeyInfo(key);
                        return geminiRemoteService.streamGeminiCompletion(req, chatContext);
                    });

            return executeStreamWithClientManagement(streamOperation, "Gemini 流式请求", chatContext);
        });
    }

    /**
     * 查询veo3模型信息
     * 从数据库中获取veo3模型的基本信息和价格数据
     */
    public Mono<Object> getVeo3ModelInfo() {
        return getModelInfo("veo3");
    }

    /**
     * 查询指定模型信息
     * 从数据库中获取指定模型的基本信息和价格数据
     */
    public Mono<Object> getModelInfo(String modelName) {
        return Mono.fromCallable(() -> {
            // 查询模型信息
            SystemModelInfo modelInfo = systemModelInfoRepository.findSystemModelInfoByModelName(modelName);

            if (modelInfo == null) {
                // 如果没有找到模型，返回空结果
                Map<String, Object> result = new HashMap<>();
                result.put("error", modelName + "模型未找到");
                result.put("modelName", modelName);
                result.put("available", false);
                return result;
            }

            // 查询价格信息
            SystemLLMPrice priceInfo = systemLLMPriceRepository.findBySystemModelId(modelInfo.getId());

            // 构建返回结果
            Map<String, Object> result = buildModelInfoResponse(modelInfo, priceInfo);

            log.info("查询{}模型信息成功: {}", modelName, result);
            return result;
        }).doOnError(error -> {
            log.error("查询{}模型信息失败", modelName, error);
        });
    }

    /**
     * 构建模型信息响应
     */
    private Map<String, Object> buildModelInfoResponse(SystemModelInfo modelInfo, SystemLLMPrice priceInfo) {
        Map<String, Object> result = new HashMap<>();
        result.put("id", modelInfo.getId());
        result.put("modelName", modelInfo.getModelName());
        result.put("manufacturerName", modelInfo.getManufacturerName());
        result.put("vendorNames", modelInfo.getVendorNames());
        result.put("modelType", modelInfo.getModelType());
        result.put("logoUrl", modelInfo.getLogoUrl());
        result.put("createTime", modelInfo.getCreateTime());
        result.put("statuses", modelInfo.getStatuses());
        result.put("available", modelInfo.getStatuses() == 1);

        // 添加价格信息
        if (priceInfo != null) {
            Map<String, Object> prices = new HashMap<>();
            prices.put("textPrompt", priceInfo.getTextPrompt());
            prices.put("textCachePrompt", priceInfo.getTextCachePrompt());
            prices.put("textCompletion", priceInfo.getTextCompletion());
            prices.put("audioPrompt", priceInfo.getAudioPrompt());
            prices.put("audioCachePrompt", priceInfo.getAudioCachePrompt());
            prices.put("audioCompletion", priceInfo.getAudioCompletion());
            prices.put("reasoningCompletion", priceInfo.getReasoningCompletion());
            prices.put("imagePrompt", priceInfo.getImagePrompt());
            prices.put("imageCachePrompt", priceInfo.getImageCachePrompt());
            prices.put("imageCompletion", priceInfo.getImageCompletion());
            prices.put("textCachePromptWrite5M", priceInfo.getTextCachePromptWrite5M());
            prices.put("textCachePromptWrite1H", priceInfo.getTextCachePromptWrite1H());
            prices.put("searchTool", priceInfo.getSearchTool());
            result.put("prices", prices);
        }

        return result;
    }

    /**
     * 提取聊天上下文并设置请求信息
     */
    private ChatContext extractChatContext(reactor.util.context.ContextView contextView, HashMap<String, Object> req) {
        ChatContext chatContext = contextView.get("chatContext");
        chatContext.getChatRequestStatistic().setRawRequest(req);
        boolean hasGoogleSearch = false;
        if (req.containsKey("tools")) {
            Object toolsObj = req.get("tools");
            if (toolsObj instanceof List<?> toolsList) {
                for (Object tool : toolsList) {
                    if (tool instanceof Map<?, ?> toolMap) {
                        if (toolMap.containsKey("google_search")) {
                            hasGoogleSearch = true;
                            break;
                        }
                    }
                }
            }
        }
        if (hasGoogleSearch) {
            chatContext.getChatRequestStatistic().setSearchRequest(true);
        }

        return chatContext;
    }

    @Override
    protected void publishUsageStatistics(ChatContext chatContext) {
        try {
            organizationLLMCalculatePrice.organizationListen(
                    CharacterLLMStatisticsConverter.convert(chatContext),
                    chatContext.apiModelName()
            );
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布 Gemini 使用量统计详细错误信息", e);
        }
    }

    @Override
    protected void publishUsageStatisticsAsync(ChatContext chatContext) {
        // 使用智能选择方法，根据配置自动决定异步或同步处理
        asyncUsageStatisticsService.publishLLMUsageStatisticsIntelligent(
                chatContext,
                () -> publishUsageStatistics(chatContext)
        );
    }
}
